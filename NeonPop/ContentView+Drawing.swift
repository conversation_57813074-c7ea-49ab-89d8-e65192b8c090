//
//  ContentView+Drawing.swift
//  NeonPop
//
//  Created by late night king on 2025/6/16.
//

import SwiftUI
import Photos

extension ContentView {
    
    // 在画布上下文中绘制图层 - 与SwiftUI显示保持一致
    func drawLayerInCanvasContext(_ layer: LayerModel, context: UIGraphicsImageRendererContext, canvasRect: CGRect) {
        context.cgContext.saveGState()

        // 计算从显示尺寸到保存尺寸的缩放比例
        let scaleX = canvasRect.width / displayCanvasSize.width
        let scaleY = canvasRect.height / displayCanvasSize.height

        print("绘制图层 \(layer.name):")
        print("  画布矩形: \(canvasRect)")
        print("  显示画布尺寸: \(displayCanvasSize)")
        print("  缩放比例: x=\(scaleX), y=\(scaleY)")
        print("  图层位置: \(layer.transform.position)")

        // 应用变换 - 考虑尺寸缩放
        let transform = layer.transform
        let scaledPositionX = canvasRect.midX + transform.position.x * scaleX
        let scaledPositionY = canvasRect.midY + transform.position.y * scaleY

        print("  缩放后位置: x=\(scaledPositionX), y=\(scaledPositionY)")

        context.cgContext.translateBy(x: scaledPositionX, y: scaledPositionY)
        context.cgContext.scaleBy(x: transform.scale * scaleX, y: transform.scale * scaleY)
        context.cgContext.rotate(by: CGFloat(transform.rotation * .pi / 180))
        context.cgContext.setAlpha(CGFloat(transform.opacity))

        switch layer.type {
        case .image:
            if let image = layer.processedImage ?? layer.originalImage {
                // 计算合适的图像显示尺寸，确保图像适配画布
                let imageSize = image.size
                let maxDimension = max(canvasRect.width, canvasRect.height) * 0.8 // 最大不超过画布的80%

                let scale = min(maxDimension / imageSize.width, maxDimension / imageSize.height)
                let displaySize = CGSize(
                    width: imageSize.width * scale,
                    height: imageSize.height * scale
                )

                let imageRect = CGRect(x: -displaySize.width/2, y: -displaySize.height/2,
                                     width: displaySize.width, height: displaySize.height)

                // 如果应用了颜色变换，绘制粉红色版本
                if layer.isColorTransformed {
                    drawColorTransformedImage(image, in: imageRect, context: context)
                } else {
                    image.draw(in: imageRect)
                }
            }

        case .text:
            drawAdvancedTextInCanvas(layer, in: context)

        case .effect:
            // 特效图层的绘制逻辑
            break
        }

        context.cgContext.restoreGState()
    }

    // 在显示上下文中绘制图层 - 1:1比例，无缩放
    func drawLayerInDisplayContext(_ layer: LayerModel, context: UIGraphicsImageRendererContext, canvasRect: CGRect) {
        context.cgContext.saveGState()

        // 应用变换 - 直接使用显示坐标，无需缩放
        let transform = layer.transform
        context.cgContext.translateBy(x: canvasRect.midX + transform.position.x,
                                    y: canvasRect.midY + transform.position.y)
        context.cgContext.scaleBy(x: transform.scale, y: transform.scale)
        context.cgContext.rotate(by: CGFloat(transform.rotation * .pi / 180))
        context.cgContext.setAlpha(CGFloat(transform.opacity))

        switch layer.type {
        case .image:
            if let image = layer.processedImage ?? layer.originalImage {
                // 计算合适的图像显示尺寸，确保图像适配画布
                let imageSize = image.size
                let maxDimension = max(canvasRect.width, canvasRect.height) * 0.8 // 最大不超过画布的80%

                let scale = min(maxDimension / imageSize.width, maxDimension / imageSize.height)
                let displaySize = CGSize(
                    width: imageSize.width * scale,
                    height: imageSize.height * scale
                )

                let imageRect = CGRect(x: -displaySize.width/2, y: -displaySize.height/2,
                                     width: displaySize.width, height: displaySize.height)

                // 如果应用了颜色变换，绘制粉红色版本
                if layer.isColorTransformed {
                    drawColorTransformedImage(image, in: imageRect, context: context)
                } else {
                    image.draw(in: imageRect)
                }
            }

        case .text:
            drawAdvancedTextInCanvas(layer, in: context)

        case .effect:
            // 特效图层的绘制逻辑
            break
        }

        context.cgContext.restoreGState()
    }

    // 绘制颜色变换的图像
    func drawColorTransformedImage(_ image: UIImage, in rect: CGRect, context: UIGraphicsImageRendererContext) {
        // 保存当前图形状态
        context.cgContext.saveGState()

        // 设置混合模式为正片叠底，然后绘制粉红色
        context.cgContext.setBlendMode(.multiply)

        // 先绘制原图像
        image.draw(in: rect)

        // 然后用粉红色进行颜色混合
        context.cgContext.setFillColor(UIColor(CyberPunkStyle.neonPink).cgColor)
        context.cgContext.fill(rect)

        // 恢复图形状态
        context.cgContext.restoreGState()
    }

    // 在画布中绘制高级文字（支持新的文字属性）
    func drawAdvancedTextInCanvas(_ layer: LayerModel, in context: UIGraphicsImageRendererContext) {
        guard let text = layer.text else { return }

        // 计算缩放比例（用于调整模糊半径）
        let scaleX = canvasSize.width / displayCanvasSize.width
        let scaleY = canvasSize.height / displayCanvasSize.height
        let averageScale = (scaleX + scaleY) / 2

        // 创建字体
        var font: UIFont
        if layer.fontName == "System" {
            font = UIFont.systemFont(ofSize: layer.fontSize)
        } else {
            font = UIFont(name: layer.fontName, size: layer.fontSize) ?? UIFont.systemFont(ofSize: layer.fontSize)
        }

        // 应用粗体和斜体
        var traits: UIFontDescriptor.SymbolicTraits = []
        if layer.isBold {
            traits.insert(.traitBold)
        }
        if layer.isItalic {
            traits.insert(.traitItalic)
        }

        if !traits.isEmpty {
            if let descriptor = font.fontDescriptor.withSymbolicTraits(traits) {
                font = UIFont(descriptor: descriptor, size: layer.fontSize)
            }
        }

        // 创建属性字典
        var attributes: [NSAttributedString.Key: Any] = [
            .font: font,
            .foregroundColor: UIColor(layer.textColor)
        ]

        // 添加下划线
        if layer.isUnderlined {
            attributes[.underlineStyle] = NSUnderlineStyle.single.rawValue
        }

        // 创建属性字符串
        let attributedString = NSMutableAttributedString(string: text, attributes: attributes)

        // 设置段落样式（对齐方式）
        let paragraphStyle = NSMutableParagraphStyle()
        switch layer.textAlignment {
        case .leading:
            paragraphStyle.alignment = .left
        case .center:
            paragraphStyle.alignment = .center
        case .trailing:
            paragraphStyle.alignment = .right
        }

        attributedString.addAttribute(.paragraphStyle, value: paragraphStyle, range: NSRange(location: 0, length: text.count))

        // 计算文字大小和位置
        let textSize = attributedString.size()
        var textRect = CGRect(x: -textSize.width/2, y: -textSize.height/2,
                            width: textSize.width, height: textSize.height)

        // 如果是垂直布局，需要旋转上下文
        if layer.isVertical {
            context.cgContext.saveGState()
            context.cgContext.rotate(by: -CGFloat.pi / 2)
            textRect = CGRect(x: -textSize.height/2, y: -textSize.width/2,
                            width: textSize.height, height: textSize.width)
        }

        // 根据文字形状绘制文字
        switch layer.textShape {
        case .normal:
            // 正常绘制文字 - 支持高斯模糊效果
            if layer.hasBlur && layer.blurRadius > 0 {
                // 根据缩放比例调整模糊半径，并增加系数以匹配SwiftUI效果
                let scaledBlurRadius = layer.blurRadius * averageScale * 1.5 // 1.5倍系数匹配SwiftUI blur效果

                // 创建一个足够大的临时画布来容纳模糊效果
                let blurPadding = scaledBlurRadius * 3 // 增加边距以容纳模糊扩散
                let tempSize = CGSize(width: textSize.width + blurPadding * 2,
                                    height: textSize.height + blurPadding * 2)
                let tempRenderer = UIGraphicsImageRenderer(size: tempSize)

                let textImage = tempRenderer.image { tempContext in
                    // 在临时画布中心绘制文字
                    let centeredRect = CGRect(x: blurPadding, y: blurPadding,
                                            width: textSize.width, height: textSize.height)
                    attributedString.draw(in: centeredRect)
                }

                // 应用高斯模糊（使用缩放后的半径）
                if let blurredImage = applyGaussianBlurToText(image: textImage, radius: scaledBlurRadius) {
                    // 计算绘制位置
                    let drawRect = CGRect(x: textRect.origin.x - blurPadding,
                                        y: textRect.origin.y - blurPadding,
                                        width: tempSize.width, height: tempSize.height)
                    blurredImage.draw(in: drawRect)
                } else {
                    // 如果模糊失败，绘制原始文字
                    attributedString.draw(in: textRect)
                }
            } else {
                // 正常绘制文字
                attributedString.draw(in: textRect)
            }

        case .circle:
            // 圆环文字
            drawCircularText(attributedString, layer: layer, context: context, averageScale: averageScale)

        case .arc:
            // 扇形文字
            drawArcText(attributedString, layer: layer, context: context, averageScale: averageScale)
        }

        if layer.isVertical {
            context.cgContext.restoreGState()
        }
    }

    // 应用高斯模糊到文字图像 - 改进版本以匹配SwiftUI效果
    func applyGaussianBlurToText(image: UIImage, radius: CGFloat) -> UIImage? {
        guard let cgImage = image.cgImage else { return nil }

        let ciImage = CIImage(cgImage: cgImage)
        let filter = CIFilter.gaussianBlur()
        filter.inputImage = ciImage
        filter.radius = Float(radius)

        guard let outputImage = filter.outputImage else { return nil }

        // 扩展输出区域以包含完整的模糊效果
        let extent = ciImage.extent
        let expandedExtent = extent.insetBy(dx: -radius * 3, dy: -radius * 3)

        let context = CIContext()
        guard let outputCGImage = context.createCGImage(outputImage, from: expandedExtent) else { return nil }

        return UIImage(cgImage: outputCGImage, scale: image.scale, orientation: image.imageOrientation)
    }

    // 绘制圆环文字
    func drawCircularText(_ attributedString: NSAttributedString, layer: LayerModel, context: UIGraphicsImageRendererContext, averageScale: CGFloat) {
        let text = attributedString.string
        let radius: CGFloat = 80 // 圆环半径
        let angleStep = (2 * CGFloat.pi) / CGFloat(text.count)

        context.cgContext.saveGState()

        for (index, character) in text.enumerated() {
            let angle = CGFloat(index) * angleStep - CGFloat.pi / 2 // 从顶部开始
            let x = radius * cos(angle)
            let y = radius * sin(angle)

            context.cgContext.saveGState()
            context.cgContext.translateBy(x: x, y: y)
            context.cgContext.rotate(by: angle + CGFloat.pi / 2) // 字符朝向圆心

            let charString = String(character)
            let charAttributedString = NSAttributedString(string: charString, attributes: attributedString.attributes(at: 0, effectiveRange: nil))
            let charSize = charAttributedString.size()
            let charRect = CGRect(x: -charSize.width/2, y: -charSize.height/2, width: charSize.width, height: charSize.height)

            // 应用模糊效果（如果需要）
            if layer.hasBlur && layer.blurRadius > 0 {
                let scaledBlurRadius = layer.blurRadius * averageScale * 1.5
                let blurPadding = scaledBlurRadius * 3
                let tempSize = CGSize(width: charSize.width + blurPadding * 2, height: charSize.height + blurPadding * 2)
                let tempRenderer = UIGraphicsImageRenderer(size: tempSize)

                let charImage = tempRenderer.image { tempContext in
                    let centeredRect = CGRect(x: blurPadding, y: blurPadding, width: charSize.width, height: charSize.height)
                    charAttributedString.draw(in: centeredRect)
                }

                if let blurredImage = applyGaussianBlurToText(image: charImage, radius: scaledBlurRadius) {
                    let drawRect = CGRect(x: charRect.origin.x - blurPadding, y: charRect.origin.y - blurPadding, width: tempSize.width, height: tempSize.height)
                    blurredImage.draw(in: drawRect)
                } else {
                    charAttributedString.draw(in: charRect)
                }
            } else {
                charAttributedString.draw(in: charRect)
            }

            context.cgContext.restoreGState()
        }

        context.cgContext.restoreGState()
    }

    // 绘制扇形文字
    func drawArcText(_ attributedString: NSAttributedString, layer: LayerModel, context: UIGraphicsImageRendererContext, averageScale: CGFloat) {
        let text = attributedString.string
        let radius: CGFloat = 80 // 扇形半径
        let totalAngle: CGFloat = CGFloat.pi // 180度扇形
        let startAngle: CGFloat = -totalAngle / 2 // 从左侧开始
        let angleStep = totalAngle / CGFloat(max(text.count - 1, 1))

        context.cgContext.saveGState()

        for (index, character) in text.enumerated() {
            let angle = startAngle + CGFloat(index) * angleStep
            let x = radius * cos(angle)
            let y = radius * sin(angle)

            context.cgContext.saveGState()
            context.cgContext.translateBy(x: x, y: y)
            context.cgContext.rotate(by: angle + CGFloat.pi / 2) // 字符朝向圆心

            let charString = String(character)
            let charAttributedString = NSAttributedString(string: charString, attributes: attributedString.attributes(at: 0, effectiveRange: nil))
            let charSize = charAttributedString.size()
            let charRect = CGRect(x: -charSize.width/2, y: -charSize.height/2, width: charSize.width, height: charSize.height)

            // 应用模糊效果（如果需要）
            if layer.hasBlur && layer.blurRadius > 0 {
                let scaledBlurRadius = layer.blurRadius * averageScale * 1.5
                let blurPadding = scaledBlurRadius * 3
                let tempSize = CGSize(width: charSize.width + blurPadding * 2, height: charSize.height + blurPadding * 2)
                let tempRenderer = UIGraphicsImageRenderer(size: tempSize)

                let charImage = tempRenderer.image { tempContext in
                    let centeredRect = CGRect(x: blurPadding, y: blurPadding, width: charSize.width, height: charSize.height)
                    charAttributedString.draw(in: centeredRect)
                }

                if let blurredImage = applyGaussianBlurToText(image: charImage, radius: scaledBlurRadius) {
                    let drawRect = CGRect(x: charRect.origin.x - blurPadding, y: charRect.origin.y - blurPadding, width: tempSize.width, height: tempSize.height)
                    blurredImage.draw(in: drawRect)
                } else {
                    charAttributedString.draw(in: charRect)
                }
            } else {
                charAttributedString.draw(in: charRect)
            }

            context.cgContext.restoreGState()
        }

        context.cgContext.restoreGState()
    }

    // 在高质量上下文中绘制图层 - 正确处理坐标缩放
    func drawLayerInHighQualityContext(_ layer: LayerModel, context: UIGraphicsImageRendererContext, canvasRect: CGRect, qualityMultiplier: CGFloat) {
        context.cgContext.saveGState()

        // 计算画布尺寸的缩放比例（用于位置坐标）
        let canvasScaleX = canvasRect.width / displayCanvasSize.width
        let canvasScaleY = canvasRect.height / displayCanvasSize.height

        // 应用变换 - 位置按画布比例缩放，大小保持原始比例
        let transform = layer.transform
        let finalX = canvasRect.midX + transform.position.x * canvasScaleX
        let finalY = canvasRect.midY + transform.position.y * canvasScaleY

        context.cgContext.translateBy(x: finalX, y: finalY)
        context.cgContext.scaleBy(
            x: transform.scale,
            y: transform.scale
        )
        context.cgContext.rotate(by: CGFloat(transform.rotation * .pi / 180))
        context.cgContext.setAlpha(CGFloat(transform.opacity))

        switch layer.type {
        case .image:
            if let image = layer.processedImage ?? layer.originalImage {
                // 计算合适的图像显示尺寸，确保图像适配画布
                let imageSize = image.size
                let maxDimension = max(canvasRect.width, canvasRect.height) * 0.8 // 最大不超过画布的80%

                let scale = min(maxDimension / imageSize.width, maxDimension / imageSize.height)
                let displaySize = CGSize(
                    width: imageSize.width * scale,
                    height: imageSize.height * scale
                )

                let imageRect = CGRect(
                    x: -displaySize.width/2,
                    y: -displaySize.height/2,
                    width: displaySize.width,
                    height: displaySize.height
                )

                // 如果应用了颜色变换，绘制粉红色版本
                if layer.isColorTransformed {
                    drawColorTransformedImage(image, in: imageRect, context: context)
                } else {
                    image.draw(in: imageRect)
                }
            }

        case .text:
            drawAdvancedTextInHighQualityCanvas(layer, in: context, qualityMultiplier: qualityMultiplier)

        case .effect:
            // 特效图层的绘制逻辑
            break
        }

        context.cgContext.restoreGState()
    }

    // 在高质量画布中绘制高级文字
    func drawAdvancedTextInHighQualityCanvas(_ layer: LayerModel, in context: UIGraphicsImageRendererContext, qualityMultiplier: CGFloat) {
        guard let text = layer.text else { return }

        // 创建字体 - 使用原始字体大小，不需要额外缩放
        var font: UIFont
        let fontSize = layer.fontSize
        if layer.fontName == "System" {
            font = UIFont.systemFont(ofSize: fontSize)
        } else {
            font = UIFont(name: layer.fontName, size: fontSize) ?? UIFont.systemFont(ofSize: fontSize)
        }

        // 应用粗体和斜体
        var traits: UIFontDescriptor.SymbolicTraits = []
        if layer.isBold {
            traits.insert(.traitBold)
        }
        if layer.isItalic {
            traits.insert(.traitItalic)
        }

        if !traits.isEmpty {
            if let descriptor = font.fontDescriptor.withSymbolicTraits(traits) {
                font = UIFont(descriptor: descriptor, size: fontSize)
            }
        }

        // 创建属性字典
        var attributes: [NSAttributedString.Key: Any] = [
            .font: font,
            .foregroundColor: UIColor(layer.textColor)
        ]

        // 添加下划线
        if layer.isUnderlined {
            attributes[.underlineStyle] = NSUnderlineStyle.single.rawValue
        }

        // 创建属性字符串
        let attributedString = NSMutableAttributedString(string: text, attributes: attributes)

        // 设置段落样式（对齐方式）
        let paragraphStyle = NSMutableParagraphStyle()
        switch layer.textAlignment {
        case .leading:
            paragraphStyle.alignment = .left
        case .center:
            paragraphStyle.alignment = .center
        case .trailing:
            paragraphStyle.alignment = .right
        }

        attributedString.addAttribute(.paragraphStyle, value: paragraphStyle, range: NSRange(location: 0, length: text.count))

        // 计算文字大小和位置
        let textSize = attributedString.size()
        var textRect = CGRect(x: -textSize.width/2, y: -textSize.height/2,
                            width: textSize.width, height: textSize.height)

        // 如果是垂直布局，需要旋转上下文
        if layer.isVertical {
            context.cgContext.saveGState()
            context.cgContext.rotate(by: -CGFloat.pi / 2)
            textRect = CGRect(x: -textSize.height/2, y: -textSize.width/2,
                            width: textSize.height, height: textSize.width)
        }

        // 根据文字形状绘制文字
        switch layer.textShape {
        case .normal:
            // 正常绘制文字 - 支持高斯模糊效果
            if layer.hasBlur && layer.blurRadius > 0 {
                // 使用原始模糊半径，不需要额外缩放
                let scaledBlurRadius = layer.blurRadius * 1.5

                // 创建一个足够大的临时画布来容纳模糊效果
                let blurPadding = scaledBlurRadius * 3
                let tempSize = CGSize(width: textSize.width + blurPadding * 2,
                                    height: textSize.height + blurPadding * 2)
                let tempRenderer = UIGraphicsImageRenderer(size: tempSize)

                let textImage = tempRenderer.image { tempContext in
                    // 在临时画布中心绘制文字
                    let centeredRect = CGRect(x: blurPadding, y: blurPadding,
                                            width: textSize.width, height: textSize.height)
                    attributedString.draw(in: centeredRect)
                }

                // 应用高斯模糊
                if let blurredImage = applyGaussianBlurToText(image: textImage, radius: scaledBlurRadius) {
                    // 计算绘制位置
                    let drawRect = CGRect(x: textRect.origin.x - blurPadding,
                                        y: textRect.origin.y - blurPadding,
                                        width: tempSize.width, height: tempSize.height)
                    blurredImage.draw(in: drawRect)
                } else {
                    // 如果模糊失败，绘制原始文字
                    attributedString.draw(in: textRect)
                }
            } else {
                // 正常绘制文字
                attributedString.draw(in: textRect)
            }

        case .circle:
            // 圆环文字（高质量版本）
            drawCircularText(attributedString, layer: layer, context: context, averageScale: 1.0)

        case .arc:
            // 扇形文字（高质量版本）
            drawArcText(attributedString, layer: layer, context: context, averageScale: 1.0)
        }

        if layer.isVertical {
            context.cgContext.restoreGState()
        }
    }

    // 生成白板图像 - 与保存功能保持一致
    func generateCanvasImage() -> UIImage {
        // 使用显示尺寸确保一致性
        let saveSize = displayCanvasSize

        let renderer = UIGraphicsImageRenderer(size: saveSize)
        return renderer.image { context in
            let rect = CGRect(origin: .zero, size: saveSize)

            // 绘制直角蓝色背景（无圆角）
            UIColor(CyberPunkStyle.electricBlue).setFill()
            UIRectFill(rect)

            // 绘制所有可见图层，使用与显示相同的坐标系统
            let layers = getLayersSafely()
            for layer in layers where layer.isVisible {
                drawLayerInDisplayContext(layer, context: context, canvasRect: rect)
            }
        }
    }
}
