//
//  ShapedTextView.swift
//  NeonPop
//
//  Created by late night king on 2025/6/19.
//

import SwiftUI

// 自定义形状文字显示组件
struct ShapedTextView: View {
    let layer: LayerModel
    
    var body: some View {
        Group {
            if let text = layer.text, !text.isEmpty {
                switch layer.textShape {
                case .normal:
                    normalTextView(text)
                case .circle:
                    circularTextView(text)
                case .arc:
                    arcTextView(text)
                }
            }
        }
    }
    
    // 普通文字显示
    private func normalTextView(_ text: String) -> some View {
        Text(text)
            .font(getLayerFont())
            .foregroundColor(layer.textColor)
            .multilineTextAlignment(getTextAlignment())
            .rotationEffect(layer.isVertical ? .degrees(-90) : .degrees(0))
            .blur(radius: (layer.hasBlur && layer.textColor != Color(CyberPunkStyle.neonPink)) ? layer.blurRadius : 0)
    }
    
    // 圆环文字显示
    private func circularTextView(_ text: String) -> some View {
        ZStack {
            ForEach(Array(text.enumerated()), id: \.offset) { index, character in
                Text(String(character))
                    .font(getLayerFont())
                    .foregroundColor(layer.textColor)
                    .blur(radius: (layer.hasBlur && layer.textColor != Color(CyberPunkStyle.neonPink)) ? layer.blurRadius : 0)
                    .offset(
                        x: circularOffset(for: index, total: text.count).x,
                        y: circularOffset(for: index, total: text.count).y
                    )
                    .rotationEffect(.degrees(circularRotation(for: index, total: text.count)))
            }
        }
    }
    
    // 扇形文字显示
    private func arcTextView(_ text: String) -> some View {
        ZStack {
            ForEach(Array(text.enumerated()), id: \.offset) { index, character in
                Text(String(character))
                    .font(getLayerFont())
                    .foregroundColor(layer.textColor)
                    .blur(radius: (layer.hasBlur && layer.textColor != Color(CyberPunkStyle.neonPink)) ? layer.blurRadius : 0)
                    .offset(
                        x: arcOffset(for: index, total: text.count).x,
                        y: arcOffset(for: index, total: text.count).y
                    )
                    .rotationEffect(.degrees(arcRotation(for: index, total: text.count)))
            }
        }
    }
    
    // 计算圆环文字的偏移
    private func circularOffset(for index: Int, total: Int) -> CGPoint {
        let radius: CGFloat = max(layer.fontSize * 2.5, 80)
        let angleStep = (2 * CGFloat.pi) / CGFloat(total)
        let angle = CGFloat(index) * angleStep - CGFloat.pi / 2 // 从顶部开始
        
        return CGPoint(
            x: radius * cos(angle),
            y: radius * sin(angle)
        )
    }
    
    // 计算圆环文字的旋转角度
    private func circularRotation(for index: Int, total: Int) -> Double {
        let angleStep = (2 * CGFloat.pi) / CGFloat(total)
        let angle = CGFloat(index) * angleStep - CGFloat.pi / 2
        return Double(angle + CGFloat.pi / 2) * 180 / Double.pi
    }
    
    // 计算扇形文字的偏移
    private func arcOffset(for index: Int, total: Int) -> CGPoint {
        let radius: CGFloat = max(layer.fontSize * 3, 100)
        let totalAngle: CGFloat = CGFloat.pi * 0.6 // 108度扇形
        let startAngle: CGFloat = -totalAngle / 2
        
        if total == 1 {
            return CGPoint(x: radius, y: 0)
        }
        
        let angleStep = totalAngle / CGFloat(total - 1)
        let angle = startAngle + CGFloat(index) * angleStep
        
        return CGPoint(
            x: radius * cos(angle),
            y: radius * sin(angle)
        )
    }
    
    // 计算扇形文字的旋转角度
    private func arcRotation(for index: Int, total: Int) -> Double {
        let totalAngle: CGFloat = CGFloat.pi * 0.6
        let startAngle: CGFloat = -totalAngle / 2
        
        if total == 1 {
            return 0
        }
        
        let angleStep = totalAngle / CGFloat(total - 1)
        let angle = startAngle + CGFloat(index) * angleStep
        return Double(angle + CGFloat.pi / 2) * 180 / Double.pi
    }
    
    // 获取图层字体
    private func getLayerFont() -> Font {
        var font: Font
        
        if layer.fontName == "System" {
            font = .system(size: layer.fontSize)
        } else {
            font = .custom(layer.fontName, size: layer.fontSize)
        }
        
        if layer.isBold && layer.isItalic {
            return font.weight(.bold).italic()
        } else if layer.isBold {
            return font.weight(.bold)
        } else if layer.isItalic {
            return font.italic()
        }
        
        return font
    }
    
    // 获取文字对齐方式
    private func getTextAlignment() -> TextAlignment {
        return layer.textAlignment.swiftUIAlignment
    }
}
