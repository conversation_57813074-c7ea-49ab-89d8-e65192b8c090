//
//  LayerModel.swift
//  NeonPop
//
//  Created by late night king on 2025/6/16.
//

import SwiftUI
import Foundation

// 自定义文字对齐方式，支持 Codable
enum CustomTextAlignment: String, Codable, CaseIterable {
    case leading = "leading"
    case center = "center"
    case trailing = "trailing"

    // 转换为 SwiftUI 的 TextAlignment
    var swiftUIAlignment: TextAlignment {
        switch self {
        case .leading:
            return .leading
        case .center:
            return .center
        case .trailing:
            return .trailing
        }
    }

    // 从 SwiftUI 的 TextAlignment 创建
    init(from swiftUIAlignment: TextAlignment) {
        switch swiftUIAlignment {
        case .leading:
            self = .leading
        case .center:
            self = .center
        case .trailing:
            self = .trailing
        }
    }
}

// 文字形状枚举
enum TextShape: String, Codable, CaseIterable {
    case normal = "normal"      // 普通直线文字
    case circle = "circle"      // 圆环文字
    case arc = "arc"           // 扇形文字

    var displayName: String {
        switch self {
        case .normal:
            return "普通"
        case .circle:
            return "圆环"
        case .arc:
            return "扇形"
        }
    }
}

// 图层类型
enum LayerType: String, Codable {
    case image = "image"
    case text = "text"
    case effect = "effect"
}

// 图层变换信息
struct LayerTransform: Codable, Equatable {
    var position: CGPoint = .zero
    var scale: CGFloat = 1.0
    var rotation: Double = 0.0
    var opacity: Double = 1.0
    
    static let identity = LayerTransform()
}

// 图层模型
class LayerModel: ObservableObject, Identifiable, Codable {
    let id: UUID
    @Published var name: String
    @Published var type: LayerType
    @Published var transform: LayerTransform
    @Published var isVisible: Bool = true
    @Published var isSelected: Bool = false
    @Published var isLocked: Bool = false
    
    // 图像相关属性
    @Published var originalImage: UIImage?
    @Published var processedImage: UIImage?
    
    // 文字相关属性
    @Published var text: String?
    @Published var textColor: Color = .white
    @Published var fontSize: CGFloat = 24
    @Published var fontName: String = "System"
    @Published var isBold: Bool = false
    @Published var isItalic: Bool = false
    @Published var isUnderlined: Bool = false
    @Published var textAlignment: CustomTextAlignment = .center
    @Published var isVertical: Bool = false // 是否垂直布局
    @Published var hasBlur: Bool = false // 是否有高斯模糊效果
    @Published var blurRadius: CGFloat = 3 // 模糊半径，默认为3
    @Published var textShape: TextShape = .normal // 文字形状

    // 配对文字相关属性
    @Published var pairedLayerId: UUID? = nil // 配对的图层ID
    @Published var isPairedPrimary: Bool = true // 是否为配对中的主要文字（标题文字）

    // 颜色变换相关属性
    @Published var isColorTransformed: Bool = false // 是否应用了颜色变换
    
    // 编码键
    enum CodingKeys: String, CodingKey {
        case id, name, type, transform, isVisible, isSelected, isLocked
        case text, textColor, fontSize, fontName, isBold, isItalic, isUnderlined, textAlignment, isVertical, hasBlur, blurRadius, textShape, pairedLayerId, isPairedPrimary, isColorTransformed
    }
    
    init(name: String, type: LayerType) {
        self.id = UUID()
        self.name = name
        self.type = type
        self.transform = LayerTransform.identity
    }

    // 用于从历史记录恢复的初始化方法
    init(id: UUID, name: String, type: LayerType) {
        self.id = id
        self.name = name
        self.type = type
        self.transform = LayerTransform.identity
    }
    
    // Codable 实现
    required init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        id = try container.decode(UUID.self, forKey: .id)
        name = try container.decode(String.self, forKey: .name)
        type = try container.decode(LayerType.self, forKey: .type)
        transform = try container.decode(LayerTransform.self, forKey: .transform)
        isVisible = try container.decode(Bool.self, forKey: .isVisible)
        isSelected = try container.decode(Bool.self, forKey: .isSelected)
        isLocked = try container.decode(Bool.self, forKey: .isLocked)
        text = try container.decodeIfPresent(String.self, forKey: .text)

        // 颜色解码需要特殊处理
        if let colorData = try? container.decodeIfPresent(Data.self, forKey: .textColor) {
            if let uiColor = try? NSKeyedUnarchiver.unarchivedObject(ofClass: UIColor.self, from: colorData) {
                textColor = Color(uiColor)
            }
        }

        fontSize = try container.decodeIfPresent(CGFloat.self, forKey: .fontSize) ?? 24
        fontName = try container.decodeIfPresent(String.self, forKey: .fontName) ?? "System"
        isBold = try container.decodeIfPresent(Bool.self, forKey: .isBold) ?? false
        isItalic = try container.decodeIfPresent(Bool.self, forKey: .isItalic) ?? false
        isUnderlined = try container.decodeIfPresent(Bool.self, forKey: .isUnderlined) ?? false
        textAlignment = try container.decodeIfPresent(CustomTextAlignment.self, forKey: .textAlignment) ?? .center
        isVertical = try container.decodeIfPresent(Bool.self, forKey: .isVertical) ?? false
        hasBlur = try container.decodeIfPresent(Bool.self, forKey: .hasBlur) ?? false
        blurRadius = try container.decodeIfPresent(CGFloat.self, forKey: .blurRadius) ?? 3
        textShape = try container.decodeIfPresent(TextShape.self, forKey: .textShape) ?? .normal
        pairedLayerId = try container.decodeIfPresent(UUID.self, forKey: .pairedLayerId)
        isPairedPrimary = try container.decodeIfPresent(Bool.self, forKey: .isPairedPrimary) ?? true
        isColorTransformed = try container.decodeIfPresent(Bool.self, forKey: .isColorTransformed) ?? false
    }
    
    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(id, forKey: .id)
        try container.encode(name, forKey: .name)
        try container.encode(type, forKey: .type)
        try container.encode(transform, forKey: .transform)
        try container.encode(isVisible, forKey: .isVisible)
        try container.encode(isSelected, forKey: .isSelected)
        try container.encode(isLocked, forKey: .isLocked)
        try container.encodeIfPresent(text, forKey: .text)
        
        // 颜色编码需要特殊处理
        let uiColor = UIColor(textColor)
        if let colorData = try? NSKeyedArchiver.archivedData(withRootObject: uiColor, requiringSecureCoding: false) {
            try container.encode(colorData, forKey: .textColor)
        }
        
        try container.encode(fontSize, forKey: .fontSize)
        try container.encode(fontName, forKey: .fontName)
        try container.encode(isBold, forKey: .isBold)
        try container.encode(isItalic, forKey: .isItalic)
        try container.encode(isUnderlined, forKey: .isUnderlined)
        try container.encode(textAlignment, forKey: .textAlignment)
        try container.encode(isVertical, forKey: .isVertical)
        try container.encode(hasBlur, forKey: .hasBlur)
        try container.encode(blurRadius, forKey: .blurRadius)
        try container.encode(textShape, forKey: .textShape)
        try container.encodeIfPresent(pairedLayerId, forKey: .pairedLayerId)
        try container.encode(isPairedPrimary, forKey: .isPairedPrimary)
        try container.encode(isColorTransformed, forKey: .isColorTransformed)
    }
}


