//
//  EditHistory.swift
//  NeonPop
//
//  Created by late night king on 2025/6/16.
//

import SwiftUI
import Foundation

// 编辑历史数据模型
struct EditHistory: Identifiable, Codable {
    let id = UUID()
    let title: String
    let createdAt: Date
    let modifiedAt: Date
    let thumbnailImageData: Data?
    let layers: [LayerData] // 保存图层数据
    let canvasSize: CGSize
    let aspectRatio: String
    
    // 图层数据结构（简化版，用于保存）
    struct LayerData: Codable {
        let id: UUID
        let name: String
        let type: LayerType
        let isVisible: Bool
        let isSelected: Bool
        let transform: LayerTransform
        let originalImageData: Data?
        let processedImageData: Data?
        let isColorTransformed: Bool
        
        // 文字相关属性
        let text: String?
        let textColor: CodableColor?
        let fontSize: CGFloat
        let fontName: String
        let isBold: Bool
        let isItalic: Bool
        let isUnderlined: Bool
        let textAlignment: TextAlignment
        let isVertical: Bool
        let hasBlur: Bool
        let blurRadius: CGFloat
        let textShape: TextShape
        let pairedLayerId: UUID?
        let isPairedPrimary: Bool

        enum TextShape: String, Codable {
            case normal, circle, arc
        }
        
        enum LayerType: String, Codable {
            case image, text, effect
        }
        
        enum TextAlignment: String, Codable {
            case leading, center, trailing
        }
        
        struct LayerTransform: Codable {
            let position: CGPoint
            let scale: CGFloat
            let rotation: Double
            let opacity: Double
        }
        
        // 可编码的颜色结构
        struct CodableColor: Codable {
            let red: Double
            let green: Double
            let blue: Double
            let alpha: Double
            
            init(color: Color) {
                let uiColor = UIColor(color)
                var r: CGFloat = 0, g: CGFloat = 0, b: CGFloat = 0, a: CGFloat = 0
                uiColor.getRed(&r, green: &g, blue: &b, alpha: &a)
                self.red = Double(r)
                self.green = Double(g)
                self.blue = Double(b)
                self.alpha = Double(a)
            }
            
            var color: Color {
                return Color(.sRGB, red: red, green: green, blue: blue, opacity: alpha)
            }
        }
    }
    
    init(title: String, layers: [LayerModel], canvasSize: CGSize, aspectRatio: AspectRatio, thumbnailImage: UIImage?) {
        self.title = title
        self.createdAt = Date()
        self.modifiedAt = Date()
        self.canvasSize = canvasSize
        self.aspectRatio = aspectRatio.rawValue
        
        // 转换缩略图为数据
        self.thumbnailImageData = thumbnailImage?.jpegData(compressionQuality: 0.8)
        
        // 转换图层为可保存的数据
        self.layers = layers.map { layer in
            LayerData(
                id: layer.id,
                name: layer.name,
                type: LayerData.LayerType(rawValue: layer.type.rawValue) ?? .image,
                isVisible: layer.isVisible,
                isSelected: layer.isSelected,
                transform: LayerData.LayerTransform(
                    position: layer.transform.position,
                    scale: layer.transform.scale,
                    rotation: layer.transform.rotation,
                    opacity: layer.transform.opacity
                ),
                originalImageData: layer.originalImage?.jpegData(compressionQuality: 0.9),
                processedImageData: layer.processedImage?.jpegData(compressionQuality: 0.9),
                isColorTransformed: layer.isColorTransformed,
                text: layer.text,
                textColor: LayerData.CodableColor(color: layer.textColor),
                fontSize: layer.fontSize,
                fontName: layer.fontName,
                isBold: layer.isBold,
                isItalic: layer.isItalic,
                isUnderlined: layer.isUnderlined,
                textAlignment: LayerData.TextAlignment(rawValue: layer.textAlignment.rawValue) ?? .center,
                isVertical: layer.isVertical,
                hasBlur: layer.hasBlur,
                blurRadius: layer.blurRadius,
                textShape: LayerData.TextShape(rawValue: layer.textShape.rawValue) ?? .normal,
                pairedLayerId: layer.pairedLayerId,
                isPairedPrimary: layer.isPairedPrimary
            )
        }
    }
    
    // 从历史记录恢复图层
    func restoreLayers() -> [LayerModel] {
        return layers.map { layerData in
            let layer = LayerModel(id: layerData.id, name: layerData.name, type: LayerType(rawValue: layerData.type.rawValue) ?? .image)
            layer.isVisible = layerData.isVisible
            layer.isSelected = layerData.isSelected
            layer.transform.position = layerData.transform.position
            layer.transform.scale = layerData.transform.scale
            layer.transform.rotation = layerData.transform.rotation
            layer.transform.opacity = layerData.transform.opacity
            layer.isColorTransformed = layerData.isColorTransformed
            
            // 恢复图像数据
            if let originalImageData = layerData.originalImageData {
                layer.originalImage = UIImage(data: originalImageData)
            }
            if let processedImageData = layerData.processedImageData {
                layer.processedImage = UIImage(data: processedImageData)
            }
            
            // 恢复文字属性
            layer.text = layerData.text
            layer.textColor = layerData.textColor?.color ?? .white
            layer.fontSize = layerData.fontSize
            layer.fontName = layerData.fontName
            layer.isBold = layerData.isBold
            layer.isItalic = layerData.isItalic
            layer.isUnderlined = layerData.isUnderlined
            layer.textAlignment = CustomTextAlignment(rawValue: layerData.textAlignment.rawValue) ?? .center
            layer.isVertical = layerData.isVertical
            layer.hasBlur = layerData.hasBlur
            layer.blurRadius = layerData.blurRadius
            layer.textShape = TextShape(rawValue: layerData.textShape.rawValue) ?? .normal
            layer.pairedLayerId = layerData.pairedLayerId
            layer.isPairedPrimary = layerData.isPairedPrimary
            
            return layer
        }
    }
    
    // 获取缩略图
    var thumbnailImage: UIImage? {
        guard let data = thumbnailImageData else { return nil }
        return UIImage(data: data)
    }
}
